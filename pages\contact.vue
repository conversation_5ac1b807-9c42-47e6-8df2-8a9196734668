<template>
  <div class="contact-page">
    <section class="page-hero section-sm bg-secondary">
      <div class="container">
        <div class="text-center text-white">
          <h1>Contact Us</h1>
          <p class="lead">Get in touch for your energy and climate control needs</p>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <div class="text-center">
          <h2>Contact Page Coming Soon</h2>
          <p>Detailed contact information and contact form will be available here soon.</p>
          <div class="contact-info mt-4">
            <p><strong>Phone:</strong> <a href="tel:+263123456789">+263 123 456 789</a></p>
            <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>WhatsApp:</strong> <a href="https://wa.me/263123456789">Message Us</a></p>
          </div>
          <NuxtLink to="/" class="btn btn-secondary mt-3">Back to Home</NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useHead({
  title: 'Contact Us - Tidebit',
  meta: [
    {
      name: 'description',
      content: 'Contact Tidebit for professional solar, refrigeration, and air conditioning services across Zimbabwe.'
    }
  ]
})
</script>

<style scoped>
.contact-page {
  padding-top: 80px;
}

.page-hero {
  background: linear-gradient(135deg, var(--tidebit-blue), var(--tidebit-green));
}

.lead {
  font-size: 1.2rem;
  margin-bottom: 0;
}

.contact-info a {
  color: var(--tidebit-green);
  text-decoration: none;
}

.contact-info a:hover {
  color: var(--tidebit-blue);
}
</style>