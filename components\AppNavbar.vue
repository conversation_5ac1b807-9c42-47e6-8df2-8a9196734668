<template>
  <nav class="navbar" :class="{ 'navbar-scrolled': isScrolled }">
    <div class="container">
      <div class="navbar-content">
        <!-- Logo -->
        <div class="navbar-brand">
          <NuxtLink to="/" class="brand-link">
            <span class="brand-text">Tidebit</span>
          </NuxtLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="navbar-nav desktop-nav">
          <NuxtLink to="/" class="nav-link" :class="{ active: $route.path === '/' }">
            Home
          </NuxtLink>
          <NuxtLink to="/About" class="nav-link" :class="{ active: $route.path === '/About' }">
            About
          </NuxtLink>
          <NuxtLink to="/services" class="nav-link" :class="{ active: $route.path === '/services' }">
            Services
          </NuxtLink>
          <NuxtLink to="/portfolio" class="nav-link" :class="{ active: $route.path === '/portfolio' }">
            Portfolio
          </NuxtLink>
          <NuxtLink to="/contact" class="nav-link" :class="{ active: $route.path === '/contact' }">
            Contact
          </NuxtLink>
        </div>

        <!-- CTA Button -->
        <div class="navbar-cta desktop-nav">
          <a href="tel:+263123456789" class="btn btn-primary">Get Quote</a>
        </div>

        <!-- Mobile Menu Toggle -->
        <button 
          class="mobile-menu-toggle"
          @click="toggleMobileMenu"
          :class="{ active: isMobileMenuOpen }"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div class="mobile-nav" :class="{ active: isMobileMenuOpen }">
        <div class="mobile-nav-content">
          <NuxtLink
            to="/"
            class="mobile-nav-link"
            @click="closeMobileMenu"
            :class="{ active: $route.path === '/' }"
          >
            Home
          </NuxtLink>
          <NuxtLink
            to="/About"
            class="mobile-nav-link"
            @click="closeMobileMenu"
            :class="{ active: $route.path === '/About' }"
          >
            About
          </NuxtLink>
          <NuxtLink
            to="/services"
            class="mobile-nav-link"
            @click="closeMobileMenu"
            :class="{ active: $route.path === '/services' }"
          >
            Services
          </NuxtLink>
          <NuxtLink
            to="/portfolio"
            class="mobile-nav-link"
            @click="closeMobileMenu"
            :class="{ active: $route.path === '/portfolio' }"
          >
            Portfolio
          </NuxtLink>
          <NuxtLink
            to="/contact"
            class="mobile-nav-link"
            @click="closeMobileMenu"
            :class="{ active: $route.path === '/contact' }"
          >
            Contact
          </NuxtLink>
          <div class="mobile-nav-cta">
            <a href="tel:+263123456789" class="btn btn-primary btn-lg">Get Quote</a>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
  document.body.style.overflow = isMobileMenuOpen.value ? 'hidden' : 'auto'
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
  document.body.style.overflow = 'auto'
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.body.style.overflow = 'auto'
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  padding: 15px 0;
}

.navbar-scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-light);
  padding: 10px 0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-brand .brand-link {
  text-decoration: none;
}

.brand-text {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--tidebit-green), var(--tidebit-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  text-decoration: none;
  color: var(--tidebit-dark);
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--tidebit-green);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--tidebit-green);
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  width: 30px;
  height: 25px;
  justify-content: space-between;
}

.mobile-menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background: var(--tidebit-dark);
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-nav {
  position: fixed;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  height: calc(100vh - 70px);
  overflow-y: auto;
}

.mobile-nav.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.mobile-nav-link {
  text-decoration: none;
  color: var(--tidebit-dark);
  font-weight: 500;
  font-size: 1.2rem;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
  transition: color 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--tidebit-green);
}

.mobile-nav-cta {
  margin-top: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
}

@media (min-width: 769px) {
  .mobile-nav {
    display: none;
  }
}
</style>
