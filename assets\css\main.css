/* Tidebit Global Styles */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
}

/* Tidebit Brand Colors */
:root {
  --tidebit-green: #00a747;
  --tidebit-blue: #05abec;
  --tidebit-dark: #1a1a1a;
  --tidebit-gray: #666;
  --tidebit-light-gray: #f8f9fa;
  --tidebit-white: #ffffff;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--tidebit-dark);
}

h1 {
  font-size: 3.5rem;
  font-weight: 800;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
  color: var(--tidebit-gray);
  font-size: 1.1rem;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 30px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  line-height: 1.5;
}

.btn-primary {
  background: var(--tidebit-green);
  color: white;
}

.btn-primary:hover {
  background: #008a3a;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: var(--tidebit-blue);
  color: white;
}

.btn-secondary:hover {
  background: #0491c7;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-outline {
  background: transparent;
  color: var(--tidebit-green);
  border: 2px solid var(--tidebit-green);
}

.btn-outline:hover {
  background: var(--tidebit-green);
  color: white;
}

.btn-lg {
  padding: 16px 40px;
  font-size: 1.1rem;
}

/* Container and Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.container-fluid {
  width: 100%;
  padding: 0 20px;
}

.section {
  padding: 80px 0;
}

.section-sm {
  padding: 60px 0;
}

.section-lg {
  padding: 120px 0;
}

/* Grid System */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col {
  flex: 1;
  padding: 0 15px;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--tidebit-green); }
.text-secondary { color: var(--tidebit-blue); }
.text-dark { color: var(--tidebit-dark); }
.text-gray { color: var(--tidebit-gray); }

/* Background Utilities */
.bg-primary { background-color: var(--tidebit-green); }
.bg-secondary { background-color: var(--tidebit-blue); }
.bg-light { background-color: var(--tidebit-light-gray); }
.bg-white { background-color: var(--tidebit-white); }

/* Spacing Utilities */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

/* Card Component */
.card {
  background: white;
  border-radius: 10px;
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.card-body {
  padding: 30px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Smooth transitions for all interactive elements */
a, button, .btn, .card, .service-card, .advantage-card {
  transition: all 0.3s ease;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .section {
    padding: 60px 0;
  }
  
  .col-6,
  .col-4,
  .col-3 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  
  .btn {
    padding: 10px 25px;
    font-size: 0.9rem;
  }
  
  .btn-lg {
    padding: 14px 35px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }
  
  .section {
    padding: 40px 0;
  }
}
