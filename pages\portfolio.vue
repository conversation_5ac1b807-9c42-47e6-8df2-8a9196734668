<template>
  <div class="portfolio-page">
    <section class="page-hero section-sm bg-primary">
      <div class="container">
        <div class="text-center text-white">
          <h1>Our Portfolio</h1>
          <p class="lead">Showcasing our successful projects across Zimbabwe</p>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <div class="text-center">
          <h2>Portfolio Page Coming Soon</h2>
          <p>View our completed solar, refrigeration, and air conditioning projects here soon.</p>
          <NuxtLink to="/" class="btn btn-primary">Back to Home</NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useHead({
  title: 'Portfolio - Tidebit',
  meta: [
    {
      name: 'description',
      content: 'View Tidebit\'s portfolio of successful solar, refrigeration, and air conditioning projects across Zimbabwe.'
    }
  ]
})
</script>

<style scoped>
.portfolio-page {
  padding-top: 80px;
}

.page-hero {
  background: linear-gradient(135deg, var(--tidebit-green), var(--tidebit-blue));
}

.lead {
  font-size: 1.2rem;
  margin-bottom: 0;
}
</style>