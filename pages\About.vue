<template>
  <div class="about-page">
    <section class="page-hero section-sm bg-primary">
      <div class="container">
        <div class="text-center text-white">
          <h1>About Tidebit</h1>
          <p class="lead">Learn more about our team, mission, and commitment to excellence</p>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <div class="text-center">
          <h2>About Page Coming Soon</h2>
          <p>This page is under construction. Please check back soon for more information about Tidebit.</p>
          <NuxtLink to="/Home" class="btn btn-primary">Back to Home</NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useHead({
  title: 'About Us - Tidebit',
  meta: [
    {
      name: 'description',
      content: 'Learn more about Tidebit Incorporated, Zimbabwe\'s trusted provider of solar, refrigeration, and air conditioning solutions.'
    }
  ]
})
</script>

<style scoped>
.about-page {
  padding-top: 80px;
}

.page-hero {
  background: linear-gradient(135deg, var(--tidebit-green), var(--tidebit-blue));
}

.lead {
  font-size: 1.2rem;
  margin-bottom: 0;
}
</style>