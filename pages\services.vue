<template>
  <div class="services-page">
    <section class="page-hero section-sm bg-secondary">
      <div class="container">
        <div class="text-center text-white">
          <h1>Our Services</h1>
          <p class="lead">Comprehensive energy and climate control solutions for Zimbabwe</p>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <div class="text-center">
          <h2>Services Page Coming Soon</h2>
          <p>Detailed information about our solar, refrigeration, and air conditioning services will be available here soon.</p>
          <NuxtLink to="/Home" class="btn btn-secondary">Back to Home</NuxtLink>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
useHead({
  title: 'Services - Tidebit',
  meta: [
    {
      name: 'description',
      content: 'Explore Tidebit\'s comprehensive solar energy, refrigeration, and air conditioning services across Zimbabwe.'
    }
  ]
})
</script>

<style scoped>
.services-page {
  padding-top: 80px;
}

.page-hero {
  background: linear-gradient(135deg, var(--tidebit-blue), var(--tidebit-green));
}

.lead {
  font-size: 1.2rem;
  margin-bottom: 0;
}
</style>