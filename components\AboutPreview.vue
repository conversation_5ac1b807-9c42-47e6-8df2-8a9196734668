<template>
  <section class="about-preview section">
    <div class="container">
      <div class="row">
        <div class="col-12 col-md-6">
          <div class="about-content">
            <div class="section-header">
              <h6 class="section-subtitle text-primary">Who We Are</h6>
              <h2 class="section-title">Trusted Since 2020</h2>
            </div>
            
            <p class="about-description">
              Tidebit Incorporated is a proudly Zimbabwean company delivering high-performance solar systems, 
              cold room installations, and air conditioning services. Backed by global partners like 
              <strong class="text-primary">Haier Biomedical</strong> and <strong class="text-secondary">Samsung</strong>, 
              we combine technical precision with local expertise to serve homes, businesses, and institutions.
            </p>

            <div class="about-features">
              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-award text-primary"></i>
                </div>
                <div class="feature-content">
                  <h5>Certified Excellence</h5>
                  <p>Official Haier Biomedical service provider with certified technicians</p>
                </div>
              </div>

              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-handshake text-secondary"></i>
                </div>
                <div class="feature-content">
                  <h5>Global Partnerships</h5>
                  <p>Backed by industry leaders Samsung and Haier Biomedical</p>
                </div>
              </div>

              <div class="feature-item">
                <div class="feature-icon">
                  <i class="fas fa-map-marked-alt text-primary"></i>
                </div>
                <div class="feature-content">
                  <h5>Nationwide Coverage</h5>
                  <p>Professional installations and support across Zimbabwe</p>
                </div>
              </div>
            </div>

            <div class="about-cta">
              <NuxtLink to="/about" class="btn btn-primary">
                Learn more about our team and mission
              </NuxtLink>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-6">
          <div class="about-image">
            <div class="image-wrapper">
              <img 
                src="https://images.pexels.com/photos/4254166/pexels-photo-4254166.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop" 
                alt="Professional technicians inspecting solar panels"
                class="main-image"
              />
              <div class="image-overlay">
                <div class="overlay-content">
                  <div class="stat-item">
                    <h3 class="stat-number">4+</h3>
                    <p class="stat-label">Years of Excellence</p>
                  </div>
                  <div class="stat-item">
                    <h3 class="stat-number">500+</h3>
                    <p class="stat-label">Projects Completed</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Partner Logos -->
            <div class="partner-logos">
              <div class="partner-logo">
                <div class="logo-placeholder">
                  <span>Haier Biomedical</span>
                </div>
              </div>
              <div class="partner-logo">
                <div class="logo-placeholder">
                  <span>Samsung</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style scoped>
.about-preview {
  background: var(--tidebit-light-gray);
}

.section-header {
  margin-bottom: 2rem;
}

.section-subtitle {
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--tidebit-dark);
  margin-bottom: 0;
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--tidebit-gray);
  margin-bottom: 2.5rem;
}

.about-features {
  margin-bottom: 2.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 1.5rem;
}

.feature-icon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-light);
}

.feature-icon i {
  font-size: 1.2rem;
}

.feature-content h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--tidebit-dark);
  margin-bottom: 0.5rem;
}

.feature-content p {
  font-size: 0.95rem;
  color: var(--tidebit-gray);
  margin-bottom: 0;
}

.about-image {
  position: relative;
}

.image-wrapper {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-wrapper:hover .main-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to top,
    rgba(0, 167, 71, 0.9) 0%,
    rgba(5, 171, 236, 0.7) 100%
  );
  padding: 30px;
  color: white;
}

.overlay-content {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
}

.partner-logos {
  display: flex;
  gap: 20px;
  margin-top: 30px;
  justify-content: center;
}

.partner-logo {
  flex: 1;
  max-width: 120px;
}

.logo-placeholder {
  background: white;
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  box-shadow: var(--shadow-light);
  transition: transform 0.3s ease;
}

.logo-placeholder:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.logo-placeholder span {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--tidebit-gray);
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }
  
  .about-content {
    margin-bottom: 40px;
  }
  
  .overlay-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .partner-logos {
    justify-content: center;
    gap: 15px;
  }
  
  .feature-item {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.8rem;
  }
  
  .about-description {
    font-size: 1rem;
  }
  
  .main-image {
    height: 300px;
  }
}
</style>
