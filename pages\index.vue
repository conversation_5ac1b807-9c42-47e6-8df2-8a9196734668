<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <img
          src="https://images.pexels.com/photos/8853507/pexels-photo-8853507.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
          alt="Solar technician installing solar panels"
          class="hero-bg-image"
        />
        <div class="hero-overlay"></div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title fade-in-up">
              Power Your World. <br>
              <span class="text-secondary">Cool Your Space.</span>
            </h1>
            <p class="hero-subtitle fade-in-up">
              Durable, efficient, and affordable solar, refrigeration, and air conditioning solutions —
              installed and maintained by certified experts across Zimbabwe.
            </p>
            <div class="hero-cta fade-in-up">
              <NuxtLink to="/contact" class="btn btn-primary btn-lg">Request a Quote</NuxtLink>
              <a href="https://wa.me/263123456789" class="btn btn-secondary btn-lg" target="_blank" rel="noopener">
                <i class="fab fa-whatsapp"></i> WhatsApp Us Now
              </a>
              <a href="tel:+263123456789" class="btn btn-outline btn-lg">
                <i class="fas fa-phone"></i> Call for Consultation
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="scroll-indicator">
        <div class="scroll-arrow">
          <i class="fas fa-chevron-down"></i>
        </div>
      </div>
    </section>

    <!-- About Preview Section -->
    <AboutPreview />

    <!-- Services Snapshot Section -->
    <ServicesSnapshot />

    <!-- Why Choose Tidebit Section -->
    <WhyChooseTidebit />

    <!-- Testimonials Preview Section -->
    <TestimonialsPreview />
  </div>
</template>

<script setup>
// Set page meta
useHead({
  title: 'Tidebit - Power Your World. Cool Your Space.',
  meta: [
    {
      name: 'description',
      content: 'Durable, efficient, and affordable solar, refrigeration, and air conditioning solutions — installed and maintained by certified experts across Zimbabwe.'
    }
  ]
})

// Add scroll animations
onMounted(() => {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('fade-in-up')
      }
    })
  }, observerOptions)

  // Observe all sections
  document.querySelectorAll('section').forEach(section => {
    observer.observe(section)
  })
})
</script>

<style scoped>
.home-page {
  padding-top: 0;
}

.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
}

.hero-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 167, 71, 0.8) 0%,
    rgba(5, 171, 236, 0.6) 100%
  );
  z-index: -1;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.3rem;
  line-height: 1.6;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-cta {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-cta .btn {
  min-width: 200px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-cta .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.scroll-arrow {
  color: white;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .hero-cta .btn {
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }
}
</style>
